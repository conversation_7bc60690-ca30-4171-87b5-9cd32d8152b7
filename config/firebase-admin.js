const admin = require('firebase-admin');
const jwt = require('jsonwebtoken');
const http = require('../lib/http-errors');
const constants = require('../lib/constants');
const notificationLib = require('../lib/notification');
const User = require('../models/user');
const { translate } = require('../lib/translate');
const axios = require('axios');
const googleAuthLib = require('../lib/google-auth');

let FCM_PROJECT_ID = 'boo-dating-beta';
if (process.env.NODE_ENV == 'prod') {
  FCM_PROJECT_ID = 'boo-dating-prod';
}
const FIREBASE_ERROR_CODES = ['messaging/invalid-registration-token', 'messaging/registration-token-not-registered'];
const MAX_FCM_BATCH_SIZE = 500;

admin.initializeApp({
  credential: admin.credential.applicationDefault(),
});

// Re-use the App Check client for the lifetime of the process.
const appCheck = admin.appCheck();

function classifyAppCheckError(err) {
  if (!err?.code?.startsWith('app-check/')) return 'unknown';

  const fatal = [
    'app-check/invalid-argument',
    'app-check/token-expired',
    'app-check/token-already-consumed',
    'app-check/invalid-audience',
    'app-check/invalid-issuer',
    'app-check/invalid-subject',
    'app-check/unregistered-app',
  ];

  if (fatal.includes(err.code))       return 'unauthorized';   // 401/403
  if (err.code === 'app-check/quota-exceeded' ||
      err.message.includes('429'))    return 'rate-limit';     // 429
  if (err.code === 'app-check/internal-error') return 'transient'; // 503
  return 'unknown';
}

function sendNotif(user, category) {
  if (!user.pushNotificationSettings || !category) {
    return true;
  }

  if (user.pushNotificationSettings[category] == false) {
    return false;
  }

  return true;
}

async function removeInvalidFcmTokens(currentTokens, batchRes) {
  if (batchRes.failureCount > 0) {
    const tokensToRemove = [];
    batchRes.responses.forEach((sendRes, i) => {
      if (!sendRes.success && FIREBASE_ERROR_CODES.includes(sendRes.error.code)) {
        tokensToRemove.push(currentTokens[i]);
      }
    });
    // console.log(`removing invalid fcmTokens : ${tokensToRemove}`);
    if (tokensToRemove.length > 0) {
      await User.updateMany(
        {
          fcmToken: { $in: tokensToRemove },
        },
        {
          $set: {
            fcmToken: null,
            fcmTokenUpdatedAt: Date.now(),
          },
        },
      );
    }
  }
}

function createMessageBody({ user, title, body, data, threadId, sound, channelId, analyticsLabel, notificationId, notificationBadgeCount }) {
  let message = {
    token: user.fcmToken,
    notification: { title, body },
    apns: { payload: { aps: { sound } } },
    android: {
      priority: 'high',
      notification: { channelId, sound },
    },
  };

  if (data) {
    message.data = data;
  }

  if (notificationBadgeCount) {
    message.apns.payload.aps.badge = notificationBadgeCount;
  }

  if (threadId) {
    message.apns.payload.aps['thread-id'] = threadId.toString();
  }

  if (notificationId && user.versionAtLeast('1.13.49')) {
    message = {
      token: user.fcmToken,
      data: { ...data, notificationId: notificationId.toString() },
    };

    if (title) message.data.title = title;
    if (body) message.data.body = body;
  }

  if (analyticsLabel) {
    message.fcmOptions = { analyticsLabel };
  }

  return message;
}

/**
 * End a Live Activity on iOS via FCM.
 * This requires firebase-admin@^13.5.0 (which requires Node.js 18+)
 * If Node.js < 18, then use endLiveActivityHTTPv1 instead.
 *
 * @param {Object} params
 * @param {string} params.fcmToken - The device's FCM registration token.
 * @param {string} params.liveActivityPushToken - ActivityKit push token for THIS Live Activity.
 * @param {Object} [params.contentState] - (Optional) Final content-state your widget expects.
 * @param {number} [params.dismissalDateSec] - (Optional) UNIX seconds when the system should dismiss the activity (default: now).
 */
async function endLiveActivity({
  fcmToken,
  liveActivityPushToken,
  contentState = {},
  dismissalDateSec = Math.floor(Date.now() / 1000),
}) {
  const nowSec = Math.floor(Date.now() / 1000);

  const message = {
    token: fcmToken, // FCM registration token of the app instance
    apns: {
      // Admin SDK >= 13.5.0 supports this field
      liveActivityToken: liveActivityPushToken, // ActivityKit push token for this Live Activity
      headers: {
        // Immediate delivery
        'apns-priority': '10',
        // You generally don't need to set apns-topic when using liveActivityToken with FCM.
        // If you do set it yourself, it must be "<bundle-id>.push-type.liveactivity".
      },
      payload: {
        aps: {
          // Required keys for Live Activity end
          timestamp: nowSec,
          'dismissal-date': dismissalDateSec,
          event: 'end',

          // Include any final state your widget expects
          'content-state': contentState,

          // Optional alert shown to the user
          /*
          alert: {
            title: 'Activity ended',
            body: 'Thanks for following along.',
          },
          */
        },
      },
    },
  };
  console.log(JSON.stringify(message,null,2));

  try {
    const id = await admin.messaging().send(message);
    console.log('Sent end Live Activity push. Message ID:', id);
  } catch (err) {
    const code = err?.errorInfo?.code || err?.code;
    console.log(`Error ending Live Activity, code: ${code}, message: ${err.message}`);
  }
}

/**
 * End a Live Activity via FCM HTTP v1.
 * @param {object} params
 * @param {string} params.fcmToken - The device's FCM registration token.
 * @param {string} params.liveActivityPushToken - The ActivityKit push token for this Live Activity.
 * @param {object} [params.contentState] - Optional final content-state that matches your ContentState schema.
 * @param {number} [params.dismissalDateSec] - UNIX seconds when the Lock Screen card should be removed.
 *                                            Use <= now for immediate removal.
 */
async function endLiveActivityHTTPv1({
  fcmToken,
  liveActivityPushToken,
  contentState = {},
  dismissalDateSec = Math.floor(Date.now() / 1000), // remove immediately by default
}) {
  const accessToken = await googleAuthLib.getAccessToken(['https://www.googleapis.com/auth/firebase.messaging']);
  const nowSec = Math.floor(Date.now() / 1000);

  const url = `https://fcm.googleapis.com/v1/projects/${FCM_PROJECT_ID}/messages:send`;

  const body = {
    message: {
      token: fcmToken,
      apns: {
        // IMPORTANT: snake_case for HTTP v1
        live_activity_token: liveActivityPushToken,
        headers: {
          // Priority 10 = immediate
          'apns-priority': '10',
          // You do NOT need to set apns-push-type/topic when using live_activity_token via FCM.
        },
        payload: {
          aps: {
            timestamp: nowSec,
            'dismissal-date': dismissalDateSec, // omit to let system decide (can linger up to ~4h)
            event: 'end',

            // Optional final snapshot; omit if you don't need it
            'content-state': contentState,

            // Optional alert; omit if not needed
            // alert: { title: 'Ended', body: 'Thanks for following along.' }
          },
        },
      },
    },
  };
  console.log(`endLiveActivityHTTPv1, body: ${JSON.stringify(body,null,2)}`);

  try {
    const res = await axios.post(url, body, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });
    console.log('endLiveActivityHTTPv1, FCM message sent:', res.data); // { name: 'projects/.../messages/...' }
    return res.data;
  } catch (err) {
    // Helpful error logging
    if (err.response) {
      console.error('endLiveActivityHTTPv1, FCM HTTP v1 error:', err.response.status, JSON.stringify(err.response.data,null,2));
    } else {
      console.error('endLiveActivityHTTPv1, FCM request failed:', err.message);
    }
  }
}

async function generateMessageForNotification (user, category, originalTitle, originalBody, data, threadId, soundCategory, analyticsLabel, doTranslate, notificationId, notificationMetric){
    if (!user || !sendNotif(user, category)) {
      return;
    }

    let title = originalTitle;
    let body = originalBody;
    if (doTranslate) {
      const locale = user.locale;
      title = translate(originalTitle, locale);
      const titleEnglish = translate(originalTitle, 'en');
      body = translate(originalBody, locale);
      const bodyEnglish = translate(originalBody, 'en');

      if (locale && locale !== 'en' && (title === titleEnglish || body === bodyEnglish)) {
        return;
      }
    }

    const truncate = (text) => (text.length > constants.maxNotificationBodyLength ? text.substring(0, constants.maxNotificationBodyLength) + '...' : text);
    title = truncate(title);
    body = truncate(body);

    if (!user.fcmToken) {
      return;
    }

    const sound = `${soundCategory}.wav` || 'default';
    const channelId = soundCategory || 'boo_notification_channel';

    let notificationBadgeCount;
    if (data?.action === 'delete') {
      notificationBadgeCount = await notificationLib.decreaseNotificationBadgeCount(user._id);
    } else {
      notificationBadgeCount = await notificationLib.incrementNotificationBadgeCount(user._id);
    }

    const message = createMessageBody({
      user,
      title,
      body,
      data,
      threadId,
      sound,
      channelId,
      analyticsLabel,
      notificationId,
      notificationBadgeCount,
    });

    return message;
}

module.exports = {
  admin,

  verifyToken(req, res, next) {
    if (!req.headers.authorization) {
      return next(http.unauthenticatedError());
    }
    const idToken = req.headers.authorization;
    admin.auth().verifyIdToken(idToken)
      .then((decodedToken) => {
        console.log(`Firebase auth: User ${decodedToken.uid} ${req.method} ${req.originalUrl} ${JSON.stringify(req.body)}`);

        req.uid = decodedToken.uid;
        req.phoneNumber = decodedToken.phone_number;
        req.email = decodedToken.email;
        return next();
      }).catch((firebase_err) => {
        const decoded = jwt.decode(idToken);
        console.log(req.url, ' Firebase authentication error. idToken: ', idToken, ', decoded: ', decoded, ', firebase_err: ', firebase_err);
        return next(http.unauthenticatedError());
      });
  },

  async verifyAppCheck(req, res, next) {
    if (!req.user) {
      return next();
    }

    const token = req.header('X-Firebase-AppCheck');
    if (!token) {
      return next();
    }

    try {
      const claims = await appCheck.verifyToken(token);
      req.user.passedAppCheck = true;
      await req.user.save();
      return next();
    } catch (err) {
      console.error('App Check verification failed:', err);
      const category = classifyAppCheckError(err);
      req.user.appCheckErrorCode = err.code;
      if (category == 'unauthorized') {
        req.user.failedAppCheck = true;
      }
      await req.user.save();
      return next();
    }
  },

  async sendNotification(user, category, originalTitle, originalBody, data, threadId, soundCategory, analyticsLabel, doTranslate, notificationId, notificationMetric) {
    // Send a high priority notification

    const message = await generateMessageForNotification(user, category, originalTitle, originalBody, data, threadId, soundCategory, analyticsLabel, doTranslate, notificationId, notificationMetric);

    if (!message || !message.token) return;

    const promise = admin.messaging().send(message)
      .then(() => {
        if (data?.action !== 'delete') {
          User.incrementMetrics(user._id, ['numNotifications', notificationMetric].filter(Boolean));
        }
      })
      .catch((error) => {
        if (FIREBASE_ERROR_CODES.includes(error.code)) {
          User.removeFcmToken(user._id, message.token);
          // console.log(`User ${user._id}: removed fcmToken`);
        }
        User.incrementMetrics(user._id, ['numNotificationErrors']);
        // console.log(`User ${user._id}: Error sending notification. Error: ${error}, notification: ${JSON.stringify(message, null, 2)}`);
      });

    if (process.env.TESTING) {
      await promise;
    }
  },

  async sendBatchNotifications(messages, userIdTokenMap, data, notificationMetric) {
    if (!userIdMessageMap || userIdMessageMap.size === 0) {
      return;
    }

    for (let i = 0; i < users.length; i += MAX_FCM_BATCH_SIZE) {
      const currentUsers = users.slice(i, i + MAX_FCM_BATCH_SIZE);
      const messages = [];
      const userTokenMap = new Map(); // Map tokens to users for error handling

      // Generate messages for current batch
      for (const user of currentUsers) {


        if (message && message.token) {
          messages.push(message);
          userTokenMap.set(message.token, user);
        }
      }

      if (messages.length === 0) continue;

      const promise = admin.messaging().sendEach(messages)
        .then(async (batchResponse) => {
          console.log(`[sendBatchNotifications] Successfully sent batch: ${batchResponse.successCount} success, ${batchResponse.failureCount} failures`);

          // Handle successful notifications
          if (batchResponse.successCount > 0) {
            const successfulUsers = [];
            batchResponse.responses.forEach((response, index) => {
              if (response.success) {
                const token = messages[index].token;
                const user = userTokenMap.get(token);
                if (user && data?.action !== 'delete') {
                  successfulUsers.push(user._id);
                }
              }
            });

            // Increment metrics for successful notifications
            if (successfulUsers.length > 0) {
              const metricsToIncrement = ['numNotifications'];
              if (notificationMetric) metricsToIncrement.push(notificationMetric)
              const promises = successfulUsers.map(userId => User.incrementMetrics(userId, metricsToIncrement));
              await Promise.all(promises);
            }
          }

          // Handle failed notifications and remove invalid tokens
          if (batchResponse.failureCount > 0) {
            const tokensToRemove = [];
            const usersWithErrors = [];

            batchResponse.responses.forEach((response, index) => {
              if (!response.success) {
                const token = messages[index].token;
                const user = userTokenMap.get(token);
                if (FIREBASE_ERROR_CODES.includes(response.error.code)) tokensToRemove.push(token);
                if (user) usersWithErrors.push(user._id);
              }
            });

            // Remove invalid FCM tokens
            if (tokensToRemove.length > 0) {
              await User.updateMany(
                { fcmToken: { $in: tokensToRemove } },
                {
                  $set: {
                    fcmToken: null,
                    fcmTokenUpdatedAt: Date.now(),
                  },
                }
              );
            }

            // Increment error metrics
            if (usersWithErrors.length > 0) {
              const errorPromises = usersWithErrors.map(userId => User.incrementMetrics(userId, ['numNotificationErrors']));
              await Promise.all(errorPromises);
            }
          }
        })
        .catch((error) => {
          console.log(`[sendBatchNotifications] Error sending batch notifications: ${error}`);
          for (const user of currentUsers) {
            User.incrementMetrics(user._id, ['numNotificationErrors']);
          }
        });

      if (process.env.TESTING) {
        await promise;
      }
    }
  },

  async sendMulticastNotifications(tokens, notification, analyticsLabel) {
    // filter out duplicate tokens
    tokens = [...new Set(tokens.filter(t => typeof t === 'string' && t.trim()))];

    console.log(`[sendMulticastNotifications] Number of tokens: ${tokens.length}`);

    for (let i = 0; i < tokens.length; i += MAX_FCM_BATCH_SIZE) {
      const currentTokens = tokens.slice(i, i + MAX_FCM_BATCH_SIZE);
      // console.log('Sending notifications for: ', currentTokens);

      const { title } = notification;
      const { body } = notification;
      const { data } = notification;
      const sound = `${notification.sound}.wav` || 'default';
      const channelId = notification.sound || 'boo_notification_channel';

      const message = {
        notification: {
          title,
          body,
        },
        tokens: currentTokens,
        apns: {
          payload: {
            aps: {
              sound,
            },
          },
        },
        android: {
          priority: 'high',
          notification: {
            channelId,
            sound,
          },
        },
      };
      if (analyticsLabel) {
        message.fcmOptions = {
          analyticsLabel,
        }
      }
      if (data) {
        message.data = data;
      }
      // console.log(`[sendMulticastNotifications] message: ${JSON.stringify(message}`);
      const promise = admin.messaging().sendEachForMulticast(message)
        .then(async (response) => {
          console.log('Successfully sent multicast: ', response);
          await removeInvalidFcmTokens(currentTokens, response);
        })
        .catch((error) => {
          console.log('Error sending multicast: ', error);
        });

      if (process.env.TESTING) {
        await promise;
      }
    }
  },

  async sendNotificationToFriends(tokens, notification, analyticsLabel) {
    // filter out duplicate tokens
    tokens = [...tokens];
    // console.log(`Number of tokens: ${tokens.length}`);
    const tokensToRemove = [];

    for (let i = 0; i < tokens.length; i++) {
      const currentToken = tokens[i];
      const { title } = notification;
      const { body } = notification;
      const { data } = notification;
      const sound = `${notification.sound}.wav` || 'default';
      const channelId = notification.sound || 'boo_notification_channel';

      const message = {
        notification: {
          title,
          body,
        },
        token: currentToken,
        apns: {
          payload: {
            aps: {
              sound,
            },
          },
        },
        android: {
          priority: 'high',
          notification: {
            channelId,
            sound,
          },
        },
      };
      if (analyticsLabel) {
        message.fcmOptions = {
          analyticsLabel,
        };
      }
      if (data) {
        message.data = data;
      }
      // console.log(message);
      const promise = admin.messaging().send(message)
        .then(() => {
          // console.log('Successfully sent notification to friend: ', currentToken);
        })
        .catch((error) => {
          if (FIREBASE_ERROR_CODES.includes(error.code)) {
            tokensToRemove.push(currentToken);
          }
        });

      if (process.env.TESTING) {
        await promise;
      }
    }
    if (tokensToRemove.length > 0) {
      await User.updateMany(
        {
          fcmToken: { $in: tokensToRemove },
        },
        {
          $set: {
            fcmToken: null,
            fcmTokenUpdatedAt: Date.now(),
          },
        },
      );
    }
  },

  endLiveActivity,
  endLiveActivityHTTPv1,
  FIREBASE_ERROR_CODES,
  generateMessageForNotification,
  MAX_FCM_BATCH_SIZE,
};
