const { expect, assert } = require('chai');
const { app, waitMs } = require('./common');
const { notifs, reset, waitFor } = require('./stub');
const { initApp, setFcmToken } = require('./helper/api');
const admin = require('../config/firebase-admin');
const User = require('../models/user');

describe('remove fcmToken on certainErrors', () => {
  beforeEach(async () => {
    // create user 0
    await initApp(0);
  });
  async function getUserFcmTokenData(userId) {
    const user = await User.findOne({ _id: userId }, { fcmToken: 1, fcmTokenUpdatedAt: 1 });
    return {
      fcmToken: user.fcmToken,
      fcmTokenUpdatedAt: user.fcmTokenUpdatedAt,
    };
  }
  it('error on sending single notification', async () => {
    let timeNow = new Date();
    await waitMs(2);
    await setFcmToken(0, 'valid_token');

    // valid token is not removed
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(20);
    let userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql('valid_token');
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());

    // invalid tokens removed on certain errors
    await setFcmToken(0, 'invalid_token_0');
    timeNow = new Date();
    await waitMs(2);
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(50);
    userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql(null);
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());

    await setFcmToken(0, 'not_reg_token_0');
    timeNow = new Date();
    await waitMs(2);
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(20);
    userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql(null);
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());

    // not removed on other errors
    timeNow = new Date();
    await setFcmToken(0, 'unknown_token_0');
    admin.sendNotification(await User.findById(0), null, 'my notification', 'my notification body');
    await waitMs(20);
    userData = await getUserFcmTokenData(0);
    expect(userData.fcmToken).to.eql('unknown_token_0');
    expect(userData.fcmTokenUpdatedAt).to.be.greaterThan(timeNow);
    expect(userData.fcmTokenUpdatedAt).to.be.lessThan(new Date());
  });
});

describe('sendBatchNotifications', () => {
  beforeEach(async () => {
    // create users 0, 1, 2
    await initApp(0);
    await initApp(1);
    await initApp(2);
  });

  async function getUserFcmTokenData(userId) {
    const user = await User.findOne({ _id: userId }, { fcmToken: 1, fcmTokenUpdatedAt: 1, metrics: 1 });
    return {
      fcmToken: user.fcmToken,
      fcmTokenUpdatedAt: user.fcmTokenUpdatedAt,
      numNotifications: user.metrics?.numNotifications || 0,
      numNotificationErrors: user.metrics?.numNotificationErrors || 0,
    };
  }

  it('should handle empty or invalid users array', async () => {
    // Test with null
    await admin.sendBatchNotifications(null, 'test', 'Title', 'Body');
    expect(notifs.numSent).to.equal(0);

    // Test with empty array
    await admin.sendBatchNotifications([], 'test', 'Title', 'Body');
    expect(notifs.numSent).to.equal(0);

    // Test with non-array
    await admin.sendBatchNotifications('not-array', 'test', 'Title', 'Body');
    expect(notifs.numSent).to.equal(0);
  });

  it('should send batch notifications to multiple users with valid tokens', async () => {
    reset();

    // Set valid FCM tokens for all users
    await setFcmToken(0, 'valid_token_0');
    await setFcmToken(1, 'valid_token_1');
    await setFcmToken(2, 'valid_token_2');

    const users = [
      await User.findById(0),
      await User.findById(1),
      await User.findById(2)
    ];

    await admin.sendBatchNotifications(users, 'test', 'Batch Test', 'This is a batch test');
    await waitMs(50);

    // Check that messages were sent
    expect(notifs.recentBatch).to.not.be.null;
    expect(notifs.recentBatch.length).to.equal(3);

    // Verify message structure
    const message = notifs.recentBatch[0];
    expect(message.token).to.be.a('string');
    expect(message.notification.title).to.equal('Batch Test');
    expect(message.notification.body).to.equal('This is a batch test');

    // Check that all users still have their tokens (no errors)
    const userData0 = await getUserFcmTokenData(0);
    const userData1 = await getUserFcmTokenData(1);
    const userData2 = await getUserFcmTokenData(2);

    expect(userData0.fcmToken).to.equal('valid_token_0');
    expect(userData1.fcmToken).to.equal('valid_token_1');
    expect(userData2.fcmToken).to.equal('valid_token_2');

    // Check that notification metrics were incremented
    expect(userData0.numNotifications).to.equal(1);
    expect(userData1.numNotifications).to.equal(1);
    expect(userData2.numNotifications).to.equal(1);
  });

  it('should handle mixed valid and invalid tokens', async () => {
    reset();

    // Set mixed tokens: valid, invalid, not registered
    await setFcmToken(0, 'valid_token_0');
    await setFcmToken(1, 'invalid_token_1');
    await setFcmToken(2, 'not_reg_token_2');

    const users = [
      await User.findById(0),
      await User.findById(1),
      await User.findById(2)
    ];

    await admin.sendBatchNotifications(users, 'test', 'Mixed Test', 'Testing mixed tokens');
    await waitMs(100);

    // Check that messages were attempted
    expect(notifs.recentBatch).to.not.be.null;
    expect(notifs.recentBatch.length).to.equal(3);

    // Check token handling
    const userData0 = await getUserFcmTokenData(0);
    const userData1 = await getUserFcmTokenData(1);
    const userData2 = await getUserFcmTokenData(2);

    // Valid token should remain
    expect(userData0.fcmToken).to.equal('valid_token_0');
    expect(userData0.numNotifications).to.equal(1);
    expect(userData0.numNotificationErrors).to.equal(0);

    // Invalid tokens should be removed
    expect(userData1.fcmToken).to.be.null;
    expect(userData1.numNotifications).to.equal(0);
    expect(userData1.numNotificationErrors).to.equal(1);

    expect(userData2.fcmToken).to.be.null;
    expect(userData2.numNotifications).to.equal(0);
    expect(userData2.numNotificationErrors).to.equal(1);
  });

  it('should skip users without FCM tokens', async () => {
    reset();

    // Set tokens for only some users
    await setFcmToken(0, 'valid_token_0');
    // User 1 has no token
    await setFcmToken(2, 'valid_token_2');

    const users = [
      await User.findById(0),
      await User.findById(1), // No FCM token
      await User.findById(2)
    ];

    await admin.sendBatchNotifications(users, 'test', 'Skip Test', 'Testing token skipping');
    await waitMs(50);

    // Should only send to users with tokens
    expect(notifs.recentBatch).to.not.be.null;
    expect(notifs.recentBatch.length).to.equal(2);

    // Check metrics
    const userData0 = await getUserFcmTokenData(0);
    const userData1 = await getUserFcmTokenData(1);
    const userData2 = await getUserFcmTokenData(2);

    expect(userData0.numNotifications).to.equal(1);
    expect(userData1.numNotifications).to.equal(0); // No token, no notification
    expect(userData2.numNotifications).to.equal(1);
  });

  it('should handle notification settings and categories', async () => {
    reset();

    await setFcmToken(0, 'valid_token_0');

    const user = await User.findById(0);

    // Test with category that should be blocked
    user.pushNotificationSettings.dailyPush = false;
    await user.save();

    const blockedUser = await User.findById(0); // Refresh user object
    await admin.sendBatchNotifications([blockedUser], 'dailyPush', 'Blocked Test', 'Should be blocked');
    await waitMs(50);

    // Should not send notification
    expect(notifs.recentBatch).to.be.null;

    // Reset for next test
    reset();

    // Test with allowed category
    user.pushNotificationSettings.dailyPush = true;
    await user.save();

    const allowedUser = await User.findById(0); // Refresh user object
    await admin.sendBatchNotifications([allowedUser], 'dailyPush', 'Allowed Test', 'Should be allowed');
    await waitMs(50);

    // Should send notification
    expect(notifs.recentBatch).to.not.be.null;
    expect(notifs.recentBatch.length).to.equal(1);
  });

  it('should handle custom notification metrics', async () => {
    reset();

    await setFcmToken(0, 'valid_token_0');
    const user = await User.findById(0);

    await admin.sendBatchNotifications([user], 'test', 'Metric Test', 'Testing custom metrics', null, null, null, null, false, null, 'numNotificationsDailyAI');
    await waitMs(50);

    // Check that custom metric was incremented
    const updatedUser = await User.findById(0);
    expect(updatedUser.metrics.numNotifications).to.equal(1);
    expect(updatedUser.metrics.numNotificationsDailyAI).to.equal(1);
  });
});
