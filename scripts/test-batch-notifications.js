const { sendBatchNotifications } = require('../config/firebase-admin');
const User = require('../models/user');

// GOOGLE_APPLICATION_CREDENTIALS environment variable should be set

/**
 * Test script for sendBatchNotifications function
 * 
 * Usage:
 * 1. Set your FCM tokens in the fcmTokens array below
 * 2. Optionally modify the test notification content
 * 3. Run: node scripts/test-batch-notifications.js
 * 
 * This script will:
 * - Create mock user objects with the provided FCM tokens
 * - Send batch notifications using sendBatchNotifications
 * - Test various scenarios (valid tokens, invalid tokens, mixed batch)
 */

(async () => {
  try {
    console.log('🚀 Starting batch notifications test...\n');

    // ========================================
    // CONFIGURATION - Update these values
    // ========================================
    
    const fcmTokens = [
      // Add your real FCM tokens here for testing
      'fpnQClLcPk7ho_BVBl7pkW:APA91bG-nq-NiZeV0D6RiD0A66dRsBQs-YeVOhdhgdn3-texQtTnJWTWym9WNu_seGOb-_tmnar7HoZAELGk70GzWiZojVh9mEYgzOQ2iYNy2bdCcC2ZM7bp019YGUZ8OY8uwQMTUjNa',
      // 'your_second_fcm_token_here',
      // 'your_third_fcm_token_here',
    ];

    const testNotification = {
      category: 'test',
      title: 'Batch Test Notification',
      body: 'This is a test of the sendBatchNotifications function',
      data: { 
        action: 'test',
        testId: Date.now().toString()
      },
      soundCategory: 'default',
      analyticsLabel: 'batch-test',
      doTranslate: false,
      notificationMetric: 'numTestNotifications'
    };

    // ========================================
    // TEST 1: Basic batch notification test
    // ========================================
    
    console.log('📱 Test 1: Basic batch notification with valid tokens');
    console.log(`Tokens to test: ${fcmTokens.length}`);
    
    if (fcmTokens.length === 0) {
      console.log('⚠️  No FCM tokens provided. Please add your tokens to the fcmTokens array.');
      return;
    }

    // Create mock user objects with FCM tokens
    const mockUsers = fcmTokens.map((token, index) => ({
      _id: `test_user_${index}`,
      fcmToken: token,
      pushNotificationSettings: {
        [testNotification.category]: true
      },
      locale: 'en',
      versionAtLeast: (version) => true, // Mock version check
    }));

    console.log('Sending batch notification...');
    await sendBatchNotifications(
      mockUsers,
      testNotification.category,
      testNotification.title,
      testNotification.body,
      testNotification.data,
      null, // threadId
      testNotification.soundCategory,
      testNotification.analyticsLabel,
      testNotification.doTranslate,
      null, // notificationId
      testNotification.notificationMetric
    );

    console.log('✅ Test 1 completed successfully!\n');

    // ========================================
    // TEST 2: Test with mixed valid/invalid tokens
    // ========================================
    
    console.log('📱 Test 2: Mixed batch with valid and invalid tokens');
    
    const mixedTokens = [
      ...fcmTokens.slice(0, 1), // Use first valid token
      'invalid_token_test_123', // This should fail
      'another_invalid_token_456', // This should also fail
    ];

    const mixedMockUsers = mixedTokens.map((token, index) => ({
      _id: `mixed_user_${index}`,
      fcmToken: token,
      pushNotificationSettings: {
        [testNotification.category]: true
      },
      locale: 'en',
      versionAtLeast: (version) => true,
    }));

    console.log(`Testing with ${mixedTokens.length} tokens (${fcmTokens.length} valid, ${mixedTokens.length - fcmTokens.length} invalid)`);
    
    await sendBatchNotifications(
      mixedMockUsers,
      testNotification.category,
      'Mixed Batch Test',
      'Testing with valid and invalid tokens',
      { action: 'mixed_test' },
      null,
      testNotification.soundCategory,
      'mixed-batch-test',
      false,
      null,
      'numMixedTestNotifications'
    );

    console.log('✅ Test 2 completed successfully!\n');

    // ========================================
    // TEST 3: Test with notification settings
    // ========================================
    
    console.log('📱 Test 3: Testing notification settings (blocked category)');
    
    const blockedUsers = fcmTokens.slice(0, 1).map((token, index) => ({
      _id: `blocked_user_${index}`,
      fcmToken: token,
      pushNotificationSettings: {
        [testNotification.category]: false // This should block the notification
      },
      locale: 'en',
      versionAtLeast: (version) => true,
    }));

    console.log('Sending notification with blocked category (should not send)...');
    
    await sendBatchNotifications(
      blockedUsers,
      testNotification.category,
      'Blocked Test',
      'This notification should be blocked',
      { action: 'blocked_test' },
      null,
      testNotification.soundCategory,
      'blocked-test',
      false,
      null,
      'numBlockedTestNotifications'
    );

    console.log('✅ Test 3 completed successfully!\n');

    // ========================================
    // TEST 4: Large batch test (if enough tokens)
    // ========================================
    
    if (fcmTokens.length >= 3) {
      console.log('📱 Test 4: Large batch test');
      
      // Create a larger batch by duplicating tokens (for testing purposes)
      const largeBatchTokens = [];
      for (let i = 0; i < 10; i++) {
        largeBatchTokens.push(...fcmTokens);
      }

      const largeBatchUsers = largeBatchTokens.map((token, index) => ({
        _id: `large_batch_user_${index}`,
        fcmToken: token,
        pushNotificationSettings: {
          [testNotification.category]: true
        },
        locale: 'en',
        versionAtLeast: (version) => true,
      }));

      console.log(`Testing large batch with ${largeBatchUsers.length} users...`);
      
      await sendBatchNotifications(
        largeBatchUsers,
        testNotification.category,
        'Large Batch Test',
        `Testing batch processing with ${largeBatchUsers.length} notifications`,
        { action: 'large_batch_test', batchSize: largeBatchUsers.length.toString() },
        null,
        testNotification.soundCategory,
        'large-batch-test',
        false,
        null,
        'numLargeBatchTestNotifications'
      );

      console.log('✅ Test 4 completed successfully!\n');
    } else {
      console.log('⏭️  Skipping Test 4: Need at least 3 FCM tokens for large batch test\n');
    }

    console.log('🎉 All batch notification tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Tested with ${fcmTokens.length} real FCM token(s)`);
    console.log('- Verified basic batch functionality');
    console.log('- Tested error handling with invalid tokens');
    console.log('- Tested notification settings filtering');
    if (fcmTokens.length >= 3) {
      console.log('- Tested large batch processing');
    }
    console.log('\n✅ sendBatchNotifications is working correctly!');

  } catch (error) {
    console.error('❌ Error during batch notifications test:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
})();
