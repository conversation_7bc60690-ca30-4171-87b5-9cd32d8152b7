const { sendBatchNotifications } = require('../config/firebase-admin');

// GOOGLE_APPLICATION_CREDENTIALS environment variable should be set

(async () => {
  try {
    console.log('Testing sendBatchNotifications with mixed valid/invalid tokens...\n');

    const validToken1 = 'your_first_valid_fcm_token_here';  // Replace with your first FCM token
    const validToken2 = 'your_second_valid_fcm_token_here'; // Replace with your second FCM token

    const mockUsers = [
      {
        _id: 'user1',
        fcmToken: validToken1, // Your first valid token
        pushNotificationSettings: { test: true },
        locale: 'en',
        versionAtLeast: () => true,
      },
      {
        _id: 'user2',
        fcmToken: validToken2, // Your second valid token
        pushNotificationSettings: { test: true },
        locale: 'en',
        versionAtLeast: () => true,
      },
      {
        _id: 'user3',
        fcmToken: 'invalid_token_123', // Invalid token
        pushNotificationSettings: { test: true },
        locale: 'en',
        versionAtLeast: () => true,
      }
    ];

    const result = await sendBatchNotifications(
      mockUsers,
      'test', // category
      'Test Batch Notification', // title
      'Testing mixed valid/invalid tokens' // body
    );

  } catch (error) {
    console.error('Error during batch notifications test:', error);
  }
})();
