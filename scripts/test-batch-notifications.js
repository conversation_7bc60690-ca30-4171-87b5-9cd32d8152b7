const { sendBatchNotifications } = require('../config/firebase-admin');

// GOOGLE_APPLICATION_CREDENTIALS environment variable should be set

/**
 * Minimal test script for sendBatchNotifications function
 * Tests mixed valid/invalid tokens to verify sendEach functionality
 *
 * Usage:
 * 1. Add your real FCM token in the validToken variable
 * 2. Run: node scripts/test-batch-notifications.js
 */

(async () => {
  try {
    console.log('🚀 Testing sendBatchNotifications with mixed valid/invalid tokens...\n');

    // ========================================
    // CONFIGURATION - Update with your FCM token
    // ========================================

    const validToken = 'your_valid_fcm_token_here'; // Replace with your actual FCM token

    // Create mock users with mixed valid/invalid tokens
    const mockUsers = [
      {
        _id: 'user1',
        fcmToken: validToken, // Your valid token
        pushNotificationSettings: { test: true },
        locale: 'en',
        versionAtLeast: () => true,
      },
      {
        _id: 'user2',
        fcmToken: 'invalid_token_123', // Invalid token
        pushNotificationSettings: { test: true },
        locale: 'en',
        versionAtLeast: () => true,
      },
      {
        _id: 'user3',
        fcmToken: 'another_invalid_token_456', // Another invalid token
        pushNotificationSettings: { test: true },
        locale: 'en',
        versionAtLeast: () => true,
      }
    ];

    console.log(`Testing sendBatchNotifications with ${mockUsers.length} users:`);
    console.log(`- 1 valid token: ${validToken.substring(0, 20)}...`);
    console.log(`- 2 invalid tokens`);
    console.log('');

    // Send batch notification
    const result = await sendBatchNotifications(
      mockUsers,
      'test', // category
      'Test Batch Notification', // title
      'Testing mixed valid/invalid tokens' // body
    );

    console.log('✅ sendBatchNotifications completed');
    console.log('Check your device for the notification (should receive 1 notification)');
    console.log('Invalid tokens should be handled gracefully');

  } catch (error) {
    console.error('❌ Error during test:', error);
  }
})();
